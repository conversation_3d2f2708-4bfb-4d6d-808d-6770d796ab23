#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
360度每10度一个的接触点计算结果图
明确标注接触点和切点的具体情况
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class Full360DegreeAnalyzer:
    def __init__(self):
        """使用main.py的坐标和最左侧点逻辑"""
        
        # 过辊参数 (来自main.py)
        self.A = np.array([0.5, 80.0])  # 过辊A位置
        self.roller_radius = 2.0  # 过辊半径
        
        # 原始菱形顶点 (来自main.py)
        self.original_vertices = np.array([
            [-30, 0],   # V1 (初始接触点 - "内角")
            [-20, -4],  # V2
            [20, -4],   # V3
            [30, 0],    # V4
            [20, 4],    # V5
            [-20, 4],   # V6
        ])
        
        # 内切圆参数
        self.sharp_radius = 0.8   # 锐角内切圆半径 (V1, V4)
        self.blunt_radius = 12.0  # 钝角内切圆半径 (V2, V3, V5, V6)
        
        # 内切圆圆心
        self.circle_centers = np.array([
            [-27.85, 0.00],    # V1 锐角圆心
            [-17.69, 8.00],    # V2 钝角圆心
            [17.69, 8.00],     # V3 钝角圆心
            [27.85, 0.00],     # V4 锐角圆心
            [17.69, -8.00],    # V5 钝角圆心
            [-17.69, -8.00],   # V6 钝角圆心
        ])
        
        # 旋转中心 (来自main.py的计算方式)
        self.geometric_center = np.mean(self.original_vertices, axis=0)
        rotation_center_x_offset = 2.0
        self.rotation_center = np.array([
            self.geometric_center[0] + rotation_center_x_offset,
            self.geometric_center[1],
        ])
        
        # 上方特殊点 (来自main.py)
        self.upper_point = np.array([
            self.geometric_center[0] + rotation_center_x_offset, 4
        ])
        
        self.circle_labels = ['V1(锐角)', 'V2(钝角)', 'V3(钝角)', 'V4(锐角)', 'V5(钝角)', 'V6(钝角)']
    
    def rotate_point(self, point, angle_deg, center):
        """绕指定中心旋转点"""
        angle_rad = np.deg2rad(angle_deg)
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
        
        translated = point - center
        rotated = np.array([
            translated[0] * cos_a - translated[1] * sin_a,
            translated[0] * sin_a + translated[1] * cos_a
        ])
        return rotated + center
    
    def find_leftmost_contact_point(self, rotated_vertices, rotated_upper, angle_deg):
        """
        按照main.py的逻辑寻找最左侧接触点
        关键约束：180度之前只考虑V3、V4、V5，不包括V1、V2、V6
        """
        # 初始状态：固定连接到上方点，保持到接近90度
        if angle_deg < 85:
            return rotated_upper, "fixed_upper_point", None

        # 候选点信息
        candidate_info = []

        if angle_deg < 180:
            # 旋转小于180度：只考虑V3、V4、V5（索引2、3、4）+ (2,4)点
            valid_vertex_indices = [2, 3, 4]  # V3, V4, V5

            # 添加有效的六边形顶点
            for idx in valid_vertex_indices:
                vertex = rotated_vertices[idx]
                candidate_info.append({
                    "point": vertex,
                    "type": f"vertex_V{idx + 1}",
                    "x": vertex[0],
                    "circle_index": idx
                })

            # 添加上方点
            candidate_info.append({
                "point": rotated_upper,
                "type": "upper_point",
                "x": rotated_upper[0],
                "circle_index": None
            })

        else:
            # 旋转超过180度：考虑所有顶点
            for idx, vertex in enumerate(rotated_vertices):
                candidate_info.append({
                    "point": vertex,
                    "type": f"vertex_V{idx + 1}",
                    "x": vertex[0],
                    "circle_index": idx
                })

            # 也添加上方点
            candidate_info.append({
                "point": rotated_upper,
                "type": "upper_point",
                "x": rotated_upper[0],
                "circle_index": None
            })

        # 在候选点中寻找最左侧的点（X坐标最小）
        if not candidate_info:
            return rotated_upper, "upper_point", None

        leftmost_candidate = min(candidate_info, key=lambda x: x["x"])
        leftmost_point = leftmost_candidate["point"]
        leftmost_type = leftmost_candidate["type"]
        circle_index = leftmost_candidate["circle_index"]

        # 返回最左侧的点
        if leftmost_type == "upper_point":
            return leftmost_point, "leftmost_upper_point", None
        elif leftmost_type.startswith("vertex_"):
            vertex_name = leftmost_type.replace("vertex_", "")
            return leftmost_point, f"leftmost_{vertex_name}", circle_index
        else:
            return leftmost_point, leftmost_type, circle_index
    
    def calculate_cross_tangent_with_constraint(self, center1, radius1, center2, radius2):
        """计算交叉公切线，确保过辊A上的切点x>0.5"""
        x0, y0 = center1[0], center1[1]
        x1, y1 = center2[0], center2[1]
        R, r = radius1, radius2
        
        d = math.sqrt((x1 - x0)**2 + (y1 - y0)**2)
        
        if d <= abs(R - r) or d < R + r:
            return None
        
        L = d * R / (R + r)
        cos_alpha = (R + r) / d
        alpha = math.acos(cos_alpha)
        
        Hpq = math.atan2(y1 - y0, x1 - x0)
        Hpa = Hpq + alpha
        Hpb = Hpq - alpha
        
        # 计算两条切线的切点
        tangents = []
        
        # 第一条切线
        x2 = x0 + R * math.cos(Hpa)
        y2 = y0 + R * math.sin(Hpa)
        point_A1 = np.array([x2, y2])
        
        x3 = x1 + r * math.cos(Hpa + math.pi)
        y3 = y1 + r * math.sin(Hpa + math.pi)
        point_D1 = np.array([x3, y3])
        
        if point_A1[0] > 0.5:
            length1 = np.linalg.norm(point_D1 - point_A1)
            tangents.append({
                'roller_point': point_A1,
                'diamond_point': point_D1,
                'length': length1,
                'angle': Hpa
            })
        
        # 第二条切线
        x4 = x0 + R * math.cos(Hpb)
        y4 = y0 + R * math.sin(Hpb)
        point_A2 = np.array([x4, y4])
        
        x5 = x1 + r * math.cos(Hpb + math.pi)
        y5 = y1 + r * math.sin(Hpb + math.pi)
        point_D2 = np.array([x5, y5])
        
        if point_A2[0] > 0.5:
            length2 = np.linalg.norm(point_D2 - point_A2)
            tangents.append({
                'roller_point': point_A2,
                'diamond_point': point_D2,
                'length': length2,
                'angle': Hpb
            })
        
        if not tangents:
            return None
        
        return {
            'valid_tangents': tangents,
            'selected': tangents[0],
        }
    
    def analyze_angle(self, angle_deg):
        """分析指定角度下的接触情况"""
        
        # 旋转顶点和上方点
        rotated_vertices = []
        for vertex in self.original_vertices:
            rotated_vertex = self.rotate_point(vertex, angle_deg, self.rotation_center)
            rotated_vertices.append(rotated_vertex)
        rotated_vertices = np.array(rotated_vertices)
        
        rotated_upper = self.rotate_point(self.upper_point, angle_deg, self.rotation_center)
        
        # 找到最左侧接触点
        contact_point, contact_type, circle_index = self.find_leftmost_contact_point(
            rotated_vertices, rotated_upper, angle_deg)
        
        # 如果接触点对应某个顶点，计算对应内切圆的交叉切线
        tangent_result = None
        if circle_index is not None:
            # 旋转对应的内切圆圆心
            rotated_center = self.rotate_point(
                self.circle_centers[circle_index], angle_deg, self.rotation_center)
            
            # 确定半径
            radius = self.sharp_radius if circle_index in [0, 3] else self.blunt_radius
            
            # 计算交叉切线
            tangent_result = self.calculate_cross_tangent_with_constraint(
                self.A, self.roller_radius, rotated_center, radius)
        
        return {
            'angle': angle_deg,
            'contact_point': contact_point,
            'contact_type': contact_type,
            'circle_index': circle_index,
            'rotated_vertices': rotated_vertices,
            'rotated_upper': rotated_upper,
            'tangent_result': tangent_result,
            'rotated_centers': [self.rotate_point(center, angle_deg, self.rotation_center) 
                               for center in self.circle_centers]
        }
    
    def generate_full_360_analysis(self):
        """生成360度每10度的完整分析"""
        
        angles = range(0, 360, 10)  # 每10度一个
        results = []
        
        print("360度每10度接触点分析结果:")
        print("="*80)
        print(f"{'角度':<6} {'接触类型':<20} {'接触点坐标':<20} {'接触圆弧':<12} {'切线长度':<10} {'过辊A切点x':<12}")
        print("="*80)
        
        for angle in angles:
            result = self.analyze_angle(angle)
            results.append(result)
            
            # 格式化输出
            contact_coord = f"({result['contact_point'][0]:.1f},{result['contact_point'][1]:.1f})"
            
            if result['circle_index'] is not None:
                circle_name = self.circle_labels[result['circle_index']]
                if result['tangent_result']:
                    tangent = result['tangent_result']['selected']
                    tangent_length = f"{tangent['length']:.1f}mm"
                    roller_x = f"{tangent['roller_point'][0]:.3f}"
                else:
                    tangent_length = "无有效切线"
                    roller_x = "N/A"
            else:
                circle_name = "无"
                tangent_length = "N/A"
                roller_x = "N/A"
            
            print(f"{angle:<6} {result['contact_type']:<20} {contact_coord:<20} {circle_name:<12} {tangent_length:<10} {roller_x:<12}")
        
        return results
    
    def create_comprehensive_visualization(self, results):
        """创建综合可视化图表"""
        
        # 创建大图表
        fig = plt.figure(figsize=(20, 24))
        
        # 创建6x6的子图网格
        rows, cols = 6, 6
        
        for i, result in enumerate(results):
            if i >= rows * cols:
                break
                
            ax = plt.subplot(rows, cols, i + 1)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.2)
            
            angle = result['angle']
            ax.set_title(f'{angle}°', fontsize=10, fontweight='bold')
            
            # 绘制过辊A
            roller_circle = Circle(self.A, self.roller_radius, 
                                  fill=False, edgecolor='blue', linewidth=2)
            ax.add_patch(roller_circle)
            ax.plot(self.A[0], self.A[1], 'bo', markersize=6)
            
            # 绘制菱形轮廓
            diamond_poly = Polygon(result['rotated_vertices'], fill=False, 
                                  edgecolor='gray', linewidth=1, alpha=0.5)
            ax.add_patch(diamond_poly)
            
            # 绘制内切圆
            colors = ['red', 'green', 'green', 'red', 'green', 'green']
            
            for j, (center, color) in enumerate(zip(result['rotated_centers'], colors)):
                radius = self.sharp_radius if j in [0, 3] else self.blunt_radius
                
                # 高亮当前接触的圆
                alpha = 1.0 if j == result['circle_index'] else 0.3
                linewidth = 2 if j == result['circle_index'] else 1
                
                circle = Circle(center, radius, fill=False, edgecolor=color, 
                               linewidth=linewidth, alpha=alpha)
                ax.add_patch(circle)
                
                if j == result['circle_index']:
                    ax.plot(center[0], center[1], 'o', color=color, markersize=6)
            
            # 绘制接触点
            contact_point = result['contact_point']
            ax.plot(contact_point[0], contact_point[1], 'mo', markersize=8)
            
            # 绘制交叉切线（如果存在）
            if result['tangent_result']:
                tangent = result['tangent_result']['selected']
                
                ax.plot([tangent['roller_point'][0], tangent['diamond_point'][0]], 
                       [tangent['roller_point'][1], tangent['diamond_point'][1]], 
                       'orange', linewidth=3, alpha=0.8)
                
                # 标注切点
                ax.plot(tangent['roller_point'][0], tangent['roller_point'][1], 
                       'o', color='orange', markersize=6)
                ax.plot(tangent['diamond_point'][0], tangent['diamond_point'][1], 
                       'o', color='orange', markersize=6)
                
                # 标注信息
                info_text = f"L={tangent['length']:.1f}\nx={tangent['roller_point'][0]:.2f}"
                ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                       fontsize=8, verticalalignment='top',
                       bbox=dict(boxstyle="round,pad=0.2", facecolor='yellow', alpha=0.7))
            
            # 标注接触类型
            contact_info = result['contact_type'].replace('leftmost_', '').replace('fixed_', '')
            ax.text(0.02, 0.02, contact_info, transform=ax.transAxes, 
                   fontsize=8, verticalalignment='bottom',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='lightblue', alpha=0.7))
            
            # 设置坐标轴范围
            ax.set_xlim(-35, 35)
            ax.set_ylim(-25, 85)
            ax.tick_params(labelsize=6)
        
        plt.suptitle('360度每10度接触点和交叉切线分析\n(橙色线=交叉切线, 紫色点=接触点, 蓝色圆=过辊A)', 
                     fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()


def main():
    """主函数"""
    print("开始360度每10度接触点计算分析...")
    
    analyzer = Full360DegreeAnalyzer()
    
    # 生成完整分析
    results = analyzer.generate_full_360_analysis()
    
    # 创建可视化
    print(f"\n生成36个角度的可视化图表...")
    analyzer.create_comprehensive_visualization(results)
    
    print("\n分析完成！")


if __name__ == "__main__":
    main()
