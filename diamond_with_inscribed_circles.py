#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菱形结构内切圆优化方案
将原始菱形的锐角和钝角部分用内切圆替代
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class DiamondWithInscribedCircles:
    def __init__(self):
        """初始化菱形结构和内切圆参数"""
        
        # 原始菱形顶点坐标
        self.original_diamond_vertices = np.array([
            [-30, 0],   # V1 (左锐角)
            [-20, -4],  # V2 (左下钝角)
            [20, -4],   # V3 (右下钝角)
            [30, 0],    # V4 (右锐角)
            [20, 4],    # V5 (右上钝角)
            [-20, 4],   # V6 (左上钝角)
        ])
        
        # 内切圆参数
        self.sharp_corner_radius = 0.8   # 锐角内切圆半径 (V1, V4)
        self.blunt_corner_radius = 12.0  # 钝角内切圆半径 (V2, V3, V5, V6)
        
        # 计算角度和边长信息
        self.analyze_diamond_geometry()
        
        # 生成带内切圆的新顶点
        self.new_vertices, self.circle_centers, self.distance_differences = self.generate_vertices_with_inscribed_circles()
        
    def analyze_diamond_geometry(self):
        """分析原始菱形的几何特性"""
        vertices = self.original_diamond_vertices
        n = len(vertices)
        
        self.angles = []
        self.edge_lengths = []
        
        for i in range(n):
            # 当前顶点和相邻顶点
            prev_vertex = vertices[(i - 1) % n]
            current_vertex = vertices[i]
            next_vertex = vertices[(i + 1) % n]
            
            # 计算两条边的向量
            vec1 = prev_vertex - current_vertex
            vec2 = next_vertex - current_vertex
            
            # 计算内角
            dot_product = np.dot(vec1, vec2)
            norms_product = np.linalg.norm(vec1) * np.linalg.norm(vec2)
            angle = np.arccos(np.clip(dot_product / norms_product, -1, 1))
            self.angles.append(np.degrees(angle))
            
            # 计算边长
            edge_length = np.linalg.norm(next_vertex - current_vertex)
            self.edge_lengths.append(edge_length)
            
        print("原始菱形几何分析:")
        for i, (angle, edge_length) in enumerate(zip(self.angles, self.edge_lengths)):
            vertex_type = "锐角" if i in [0, 3] else "钝角"
            print(f"V{i+1}: 内角={angle:.1f}°, 边长={edge_length:.2f}, 类型={vertex_type}")
    
    def calculate_inscribed_circle_center(self, prev_vertex, current_vertex, next_vertex, radius):
        """计算内切圆圆心位置"""
        # 计算两条边的向量
        vec1 = prev_vertex - current_vertex
        vec2 = next_vertex - current_vertex
        
        # 归一化向量
        unit_vec1 = vec1 / np.linalg.norm(vec1)
        unit_vec2 = vec2 / np.linalg.norm(vec2)
        
        # 计算角平分线方向
        bisector = unit_vec1 + unit_vec2
        bisector = bisector / np.linalg.norm(bisector)
        
        # 计算内角的一半
        dot_product = np.dot(unit_vec1, unit_vec2)
        half_angle = np.arccos(np.clip(dot_product, -1, 1)) / 2
        
        # 计算圆心到顶点的距离
        distance_to_center = radius / np.sin(half_angle)
        
        # 圆心位置
        center = current_vertex + bisector * distance_to_center
        
        return center, half_angle
    
    def calculate_tangent_points(self, prev_vertex, current_vertex, next_vertex, center, radius):
        """计算内切圆与两条边的切点"""
        # 计算两条边的单位向量
        vec1 = prev_vertex - current_vertex
        vec2 = next_vertex - current_vertex
        unit_vec1 = vec1 / np.linalg.norm(vec1)
        unit_vec2 = vec2 / np.linalg.norm(vec2)
        
        # 计算切点（圆心到边的垂足）
        # 切点1：在边current_vertex -> prev_vertex上
        to_center1 = center - current_vertex
        proj_length1 = np.dot(to_center1, unit_vec1)
        tangent_point1 = current_vertex + unit_vec1 * proj_length1
        
        # 切点2：在边current_vertex -> next_vertex上
        to_center2 = center - current_vertex
        proj_length2 = np.dot(to_center2, unit_vec2)
        tangent_point2 = current_vertex + unit_vec2 * proj_length2
        
        return tangent_point1, tangent_point2
    
    def generate_vertices_with_inscribed_circles(self):
        """生成带内切圆的新顶点坐标"""
        vertices = self.original_diamond_vertices
        n = len(vertices)
        
        new_vertices = []
        circle_centers = []
        distance_differences = []
        
        for i in range(n):
            prev_vertex = vertices[(i - 1) % n]
            current_vertex = vertices[i]
            next_vertex = vertices[(i + 1) % n]
            
            # 确定使用的半径
            if i in [0, 3]:  # V1和V4是锐角
                radius = self.sharp_corner_radius
                corner_type = "锐角"
            else:  # V2, V3, V5, V6是钝角
                radius = self.blunt_corner_radius
                corner_type = "钝角"
            
            # 计算内切圆圆心
            center, half_angle = self.calculate_inscribed_circle_center(
                prev_vertex, current_vertex, next_vertex, radius
            )
            circle_centers.append(center)
            
            # 计算切点
            tangent_point1, tangent_point2 = self.calculate_tangent_points(
                prev_vertex, current_vertex, next_vertex, center, radius
            )
            
            # 计算原顶点到圆心的距离
            original_distance = np.linalg.norm(current_vertex - center)
            distance_diff = original_distance - radius
            distance_differences.append(distance_diff)
            
            print(f"\n{corner_type} V{i+1}:")
            print(f"  原顶点: ({current_vertex[0]:.2f}, {current_vertex[1]:.2f})")
            print(f"  圆心: ({center[0]:.2f}, {center[1]:.2f})")
            print(f"  半径: {radius}")
            print(f"  内角: {self.angles[i]:.1f}°")
            print(f"  原顶点到圆心距离: {original_distance:.2f}")
            print(f"  距离差: {distance_diff:.2f}")
            print(f"  切点1: ({tangent_point1[0]:.2f}, {tangent_point1[1]:.2f})")
            print(f"  切点2: ({tangent_point2[0]:.2f}, {tangent_point2[1]:.2f})")
            
            # 添加切点到新顶点列表
            new_vertices.extend([tangent_point1, tangent_point2])
        
        return np.array(new_vertices), circle_centers, distance_differences
    
    def visualize_comparison(self):
        """可视化原始菱形和带内切圆的菱形对比"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 左图：原始菱形
        ax1.set_aspect('equal')
        ax1.grid(True, alpha=0.3)
        ax1.set_title('原始菱形结构', fontsize=14, fontweight='bold')
        
        # 绘制原始菱形
        original_poly = Polygon(self.original_diamond_vertices, fill=False, 
                               edgecolor='blue', linewidth=2, label='原始菱形')
        ax1.add_patch(original_poly)
        
        # 标注原始顶点
        for i, vertex in enumerate(self.original_diamond_vertices):
            ax1.plot(vertex[0], vertex[1], 'bo', markersize=8)
            vertex_type = "锐角" if i in [0, 3] else "钝角"
            ax1.text(vertex[0]+1, vertex[1]+1, f'V{i+1}\n{vertex_type}\n{self.angles[i]:.1f}°', 
                    fontsize=10, fontweight='bold')
        
        ax1.set_xlim(-40, 40)
        ax1.set_ylim(-15, 15)
        ax1.legend()
        
        # 右图：带内切圆的菱形
        ax2.set_aspect('equal')
        ax2.grid(True, alpha=0.3)
        ax2.set_title('内切圆优化后的菱形结构', fontsize=14, fontweight='bold')
        
        # 绘制原始菱形轮廓（虚线）
        original_poly2 = Polygon(self.original_diamond_vertices, fill=False, 
                                edgecolor='gray', linewidth=1, linestyle='--', 
                                alpha=0.5, label='原始轮廓')
        ax2.add_patch(original_poly2)
        
        # 绘制内切圆
        sharp_legend_added = False
        blunt_legend_added = False

        for i, center in enumerate(self.circle_centers):
            if i in [0, 3]:  # 锐角
                radius = self.sharp_corner_radius
                color = 'red'
                label = f'锐角内切圆 (r={radius})' if not sharp_legend_added else ""
                sharp_legend_added = True
            else:  # 钝角
                radius = self.blunt_corner_radius
                color = 'green'
                label = f'钝角内切圆 (r={radius})' if not blunt_legend_added else ""
                blunt_legend_added = True

            circle = Circle(center, radius, fill=True, facecolor=color,
                           edgecolor=color, linewidth=2, alpha=0.3, label=label)
            ax2.add_patch(circle)

            # 标注圆心
            ax2.plot(center[0], center[1], 'o', color=color, markersize=8)
            ax2.text(center[0]+1, center[1]+1, f'C{i+1}\nr={radius}',
                    fontsize=9, color=color, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # 绘制新的顶点（切点）
        for i, vertex in enumerate(self.new_vertices):
            ax2.plot(vertex[0], vertex[1], 'ko', markersize=4)
            ax2.text(vertex[0]+0.5, vertex[1]+0.5, f'T{i+1}', fontsize=8)
        
        # 连接切点形成新的轮廓
        # 这里需要重新组织顶点顺序来形成正确的多边形
        new_poly_vertices = self.organize_vertices_for_polygon()
        if len(new_poly_vertices) > 0:
            new_poly = Polygon(new_poly_vertices, fill=False, edgecolor='black', 
                              linewidth=2, label='优化后轮廓')
            ax2.add_patch(new_poly)
        
        ax2.set_xlim(-40, 40)
        ax2.set_ylim(-15, 15)
        ax2.legend()
        
        plt.tight_layout()
        plt.show()
        
        # 打印距离差统计
        print(f"\n距离差统计:")
        print(f"锐角部分 (V1, V4): {[self.distance_differences[i] for i in [0, 3]]}")
        print(f"钝角部分 (V2, V3, V5, V6): {[self.distance_differences[i] for i in [1, 2, 4, 5]]}")
        print(f"平均距离差: {np.mean(self.distance_differences):.2f}")
        print(f"最大距离差: {np.max(self.distance_differences):.2f}")
        print(f"最小距离差: {np.min(self.distance_differences):.2f}")
    
    def organize_vertices_for_polygon(self):
        """重新组织顶点以形成正确的多边形轮廓"""
        # 按照菱形的顺序重新组织切点
        # 每个角有两个切点，需要按正确顺序连接
        organized_vertices = []

        # V1的切点 (锐角，左侧)
        organized_vertices.append(self.new_vertices[1])  # V1的第二个切点

        # V2的切点 (钝角，左下)
        organized_vertices.append(self.new_vertices[2])  # V2的第一个切点
        organized_vertices.append(self.new_vertices[3])  # V2的第二个切点

        # V3的切点 (钝角，右下)
        organized_vertices.append(self.new_vertices[4])  # V3的第一个切点
        organized_vertices.append(self.new_vertices[5])  # V3的第二个切点

        # V4的切点 (锐角，右侧)
        organized_vertices.append(self.new_vertices[6])  # V4的第一个切点

        # V5的切点 (钝角，右上)
        organized_vertices.append(self.new_vertices[8])  # V5的第一个切点
        organized_vertices.append(self.new_vertices[9])  # V5的第二个切点

        # V6的切点 (钝角，左上)
        organized_vertices.append(self.new_vertices[10]) # V6的第一个切点
        organized_vertices.append(self.new_vertices[11]) # V6的第二个切点

        # V1的第一个切点 (回到起点)
        organized_vertices.append(self.new_vertices[0])  # V1的第一个切点

        return np.array(organized_vertices)

    def generate_detailed_report(self):
        """生成详细的优化报告"""
        print("\n" + "="*60)
        print("菱形内切圆优化方案详细报告")
        print("="*60)

        print(f"\n1. 原始菱形几何特性:")
        print(f"   - 总顶点数: {len(self.original_diamond_vertices)}")
        print(f"   - 锐角数量: 2 (V1, V4)")
        print(f"   - 钝角数量: 4 (V2, V3, V5, V6)")

        print(f"\n2. 内切圆参数:")
        print(f"   - 锐角内切圆半径: {self.sharp_corner_radius} mm")
        print(f"   - 钝角内切圆半径: {self.blunt_corner_radius} mm")

        print(f"\n3. 距离差分析 (原顶点到内切圆的距离):")
        for i, diff in enumerate(self.distance_differences):
            vertex_type = "锐角" if i in [0, 3] else "钝角"
            radius = self.sharp_corner_radius if i in [0, 3] else self.blunt_corner_radius
            print(f"   V{i+1} ({vertex_type}): {diff:.3f} mm (半径: {radius} mm)")

        print(f"\n4. 统计摘要:")
        sharp_diffs = [self.distance_differences[i] for i in [0, 3]]
        blunt_diffs = [self.distance_differences[i] for i in [1, 2, 4, 5]]

        print(f"   - 锐角平均距离差: {np.mean(sharp_diffs):.3f} mm")
        print(f"   - 钝角平均距离差: {np.mean(blunt_diffs):.3f} mm")
        print(f"   - 总体平均距离差: {np.mean(self.distance_differences):.3f} mm")
        print(f"   - 最大距离差: {np.max(self.distance_differences):.3f} mm")
        print(f"   - 最小距离差: {np.min(self.distance_differences):.3f} mm")

        print(f"\n5. 优化效果:")
        print(f"   - 锐角部分材料节省: {np.sum(sharp_diffs):.3f} mm")
        print(f"   - 钝角部分材料节省: {np.sum(blunt_diffs):.3f} mm")
        print(f"   - 总材料节省: {np.sum(self.distance_differences):.3f} mm")

        return {
            'sharp_corner_savings': np.sum(sharp_diffs),
            'blunt_corner_savings': np.sum(blunt_diffs),
            'total_savings': np.sum(self.distance_differences),
            'average_distance_diff': np.mean(self.distance_differences)
        }


if __name__ == "__main__":
    # 创建菱形内切圆结构
    diamond = DiamondWithInscribedCircles()

    # 生成详细报告
    optimization_results = diamond.generate_detailed_report()

    # 可视化对比
    diamond.visualize_comparison()

    print(f"\n程序执行完成！")
    print(f"优化方案已生成，请查看可视化图表。")
