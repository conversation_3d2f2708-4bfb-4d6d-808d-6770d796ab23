#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合切线分析 - 过辊与内切圆的动态切线计算
包含外公切线和交叉公切线的完整分析
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
import math

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class ComprehensiveTangentAnalyzer:
    def __init__(self):
        """初始化综合切线分析器 - 使用main.py中的坐标设定"""

        # 过辊参数 (来自main.py)
        self.A = np.array([0.5, 80.0])  # 过辊A位置
        self.roller_radius = 2.0  # 过辊半径

        # 原始菱形顶点 (来自main.py)
        self.original_vertices = np.array([
            [-30, 0],   # V1 (初始接触点 - "内角")
            [-20, -4],  # V2
            [20, -4],   # V3
            [30, 0],    # V4
            [20, 4],    # V5
            [-20, 4],   # V6
        ])

        # 内切圆参数
        self.sharp_radius = 0.8   # 锐角内切圆半径
        self.blunt_radius = 12.0  # 钝角内切圆半径

        # 内切圆圆心 (从之前计算得出)
        self.circle_centers = np.array([
            [-27.85, 0.00],    # V1 锐角圆心
            [-17.69, 8.00],    # V2 钝角圆心
            [17.69, 8.00],     # V3 钝角圆心
            [27.85, 0.00],     # V4 锐角圆心
            [17.69, -8.00],    # V5 钝角圆心
            [-17.69, -8.00],   # V6 钝角圆心
        ])

        # 旋转中心 (来自main.py的计算方式)
        self.geometric_center = np.mean(self.original_vertices, axis=0)  # 几何中心
        rotation_center_x_offset = 2.0  # X方向偏移
        self.rotation_center = np.array([
            self.geometric_center[0] + rotation_center_x_offset,  # X = 几何中心X + 偏移
            self.geometric_center[1],  # Y = 几何中心Y (不变)
        ])
    
    def rotate_point(self, point, angle_deg, center):
        """绕指定中心旋转点"""
        angle_rad = np.deg2rad(angle_deg)
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
        
        translated = point - center
        rotated = np.array([
            translated[0] * cos_a - translated[1] * sin_a,
            translated[0] * sin_a + translated[1] * cos_a
        ])
        return rotated + center
    
    def calculate_external_tangent(self, center1, radius1, center2, radius2):
        """计算两圆的外公切线"""
        d = np.linalg.norm(center2 - center1)
        
        if d <= abs(radius1 - radius2) or d < radius1 + radius2:
            return None
        
        dx = center2[0] - center1[0]
        dy = center2[1] - center1[1]
        
        sin_alpha = abs(radius1 - radius2) / d
        alpha = np.arcsin(sin_alpha)
        base_angle = np.arctan2(dy, dx)
        
        if radius1 >= radius2:
            angle1 = base_angle + alpha
            angle2 = base_angle - alpha
        else:
            angle1 = base_angle + np.pi - alpha
            angle2 = base_angle + np.pi + alpha
        
        normal1 = np.array([-np.sin(angle1), np.cos(angle1)])
        tp1_circle1 = center1 + radius1 * normal1
        tp1_circle2 = center2 + radius2 * normal1
        
        normal2 = np.array([-np.sin(angle2), np.cos(angle2)])
        tp2_circle1 = center1 + radius1 * normal2
        tp2_circle2 = center2 + radius2 * normal2
        
        length = np.linalg.norm(tp1_circle2 - tp1_circle1)
        
        return {
            'tangent1': (tp1_circle1, tp1_circle2),
            'tangent2': (tp2_circle1, tp2_circle2),
            'length': length,
            'type': 'external'
        }
    
    def calculate_cross_tangent(self, center1, radius1, center2, radius2):
        """计算两圆的交叉公切线（内公切线）- 只选择过辊A上x>0.5的切点"""
        x0, y0 = center1[0], center1[1]
        x1, y1 = center2[0], center2[1]
        R, r = radius1, radius2

        d = math.sqrt((x1 - x0)**2 + (y1 - y0)**2)

        if d <= abs(R - r) or d < R + r:
            return None

        L = d * R / (R + r)
        cos_alpha = (R + r) / d
        alpha = math.acos(cos_alpha)

        Hpq = math.atan2(y1 - y0, x1 - x0)
        Hpa = Hpq + alpha
        Hpb = Hpq - alpha

        # 第一条切线的切点
        x2 = x0 + R * math.cos(Hpa)
        y2 = y0 + R * math.sin(Hpa)
        point_A = np.array([x2, y2])

        x3 = x1 + r * math.cos(Hpa + math.pi)
        y3 = y1 + r * math.sin(Hpa + math.pi)
        point_D = np.array([x3, y3])

        # 第二条切线的切点
        x4 = x0 + R * math.cos(Hpb)
        y4 = y0 + R * math.sin(Hpb)
        point_B = np.array([x4, y4])

        x5 = x1 + r * math.cos(Hpb + math.pi)
        y5 = y1 + r * math.sin(Hpb + math.pi)
        point_C = np.array([x5, y5])

        # 选择过辊A上x>0.5的切点
        valid_tangents = []

        if point_A[0] > 0.5:  # 第一条切线的过辊A切点满足条件
            length1 = np.linalg.norm(point_D - point_A)
            valid_tangents.append({
                'roller_point': point_A,
                'diamond_point': point_D,
                'length': length1
            })

        if point_B[0] > 0.5:  # 第二条切线的过辊A切点满足条件
            length2 = np.linalg.norm(point_C - point_B)
            valid_tangents.append({
                'roller_point': point_B,
                'diamond_point': point_C,
                'length': length2
            })

        if not valid_tangents:
            return None

        # 选择第一个有效切线
        selected = valid_tangents[0]

        return {
            'tangent1': (selected['roller_point'], selected['diamond_point']),
            'tangent2': None if len(valid_tangents) < 2 else (valid_tangents[1]['roller_point'], valid_tangents[1]['diamond_point']),
            'length': selected['length'],
            'type': 'cross',
            'all_valid': valid_tangents
        }
    
    def find_best_tangent_at_angle(self, angle_deg):
        """找到指定角度下的最佳切线"""
        # 旋转所有圆心
        rotated_centers = []
        for center in self.circle_centers:
            rotated_center = self.rotate_point(center, angle_deg, self.rotation_center)
            rotated_centers.append(rotated_center)
        
        best_result = None
        min_distance = float('inf')
        
        for i, center in enumerate(rotated_centers):
            radius = self.sharp_radius if i in [0, 3] else self.blunt_radius
            
            # 计算外公切线
            ext_result = self.calculate_external_tangent(
                self.A, self.roller_radius, center, radius)

            # 计算交叉公切线
            cross_result = self.calculate_cross_tangent(
                self.A, self.roller_radius, center, radius)
            
            # 选择距离最近的圆
            distance_to_roller = np.linalg.norm(center - self.A)
            
            if distance_to_roller < min_distance:
                min_distance = distance_to_roller
                best_result = {
                    'circle_index': i,
                    'center': center,
                    'radius': radius,
                    'distance': distance_to_roller,
                    'external': ext_result,
                    'cross': cross_result
                }
        
        return best_result
    
    def visualize_tangents_at_angle(self, angle_deg):
        """可视化指定角度下的切线"""
        result = self.find_best_tangent_at_angle(angle_deg)
        
        if not result:
            print(f"角度 {angle_deg}° 下无法计算切线")
            return
        
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(f'角度 {angle_deg}° 下的过辊切线分析', fontsize=14, fontweight='bold')
        
        # 绘制过辊
        roller_circle = Circle(self.A, self.roller_radius,
                              fill=False, edgecolor='blue', linewidth=3)
        ax.add_patch(roller_circle)
        ax.plot(self.A[0], self.A[1], 'bo', markersize=10, label='过辊A')
        
        # 旋转并绘制菱形轮廓
        rotated_vertices = []
        for vertex in self.original_vertices:
            rotated_vertex = self.rotate_point(vertex, angle_deg, self.rotation_center)
            rotated_vertices.append(rotated_vertex)
        rotated_vertices = np.array(rotated_vertices)
        
        diamond_poly = Polygon(rotated_vertices, fill=False, edgecolor='gray', 
                              linewidth=1, linestyle='--', alpha=0.5, label='菱形轮廓')
        ax.add_patch(diamond_poly)
        
        # 绘制所有内切圆
        colors = ['red', 'green', 'green', 'red', 'green', 'green']
        labels = ['V1(锐角)', 'V2(钝角)', 'V3(钝角)', 'V4(锐角)', 'V5(钝角)', 'V6(钝角)']
        
        for i, center in enumerate(self.circle_centers):
            rotated_center = self.rotate_point(center, angle_deg, self.rotation_center)
            radius = self.sharp_radius if i in [0, 3] else self.blunt_radius
            color = colors[i]
            
            alpha = 0.8 if i == result['circle_index'] else 0.3
            circle = Circle(rotated_center, radius, fill=False, 
                           edgecolor=color, linewidth=2, alpha=alpha)
            ax.add_patch(circle)
            ax.plot(rotated_center[0], rotated_center[1], 'o', color=color, markersize=6)
        
        # 高亮当前接触的圆
        highlight_circle = Circle(result['center'], result['radius'], 
                                 fill=True, facecolor='yellow', alpha=0.3)
        ax.add_patch(highlight_circle)
        
        # 绘制外公切线
        if result['external']:
            ext = result['external']
            tp1_roller, tp1_diamond = ext['tangent1']
            tp2_roller, tp2_diamond = ext['tangent2']
            
            # 选择下方的切线
            if tp1_roller[1] < tp2_roller[1]:
                selected_roller = tp1_roller
                selected_diamond = tp1_diamond
            else:
                selected_roller = tp2_roller
                selected_diamond = tp2_diamond
            
            ax.plot([selected_roller[0], selected_diamond[0]], 
                   [selected_roller[1], selected_diamond[1]], 
                   'g-', linewidth=3, alpha=0.8, 
                   label=f'外公切线 (L={ext["length"]:.2f}mm)')
            ax.plot(selected_roller[0], selected_roller[1], 'go', markersize=8)
            ax.plot(selected_diamond[0], selected_diamond[1], 'go', markersize=8)
        
        # 绘制交叉公切线
        if result['cross']:
            cross = result['cross']
            tp1_roller, tp1_diamond = cross['tangent1']

            ax.plot([tp1_roller[0], tp1_diamond[0]],
                   [tp1_roller[1], tp1_diamond[1]],
                   'orange', linewidth=3, alpha=0.8,
                   label=f'交叉公切线 (L={cross["length"]:.2f}mm)')

            # 标注切点
            ax.plot(tp1_roller[0], tp1_roller[1], 'o', color='orange', markersize=8)
            ax.plot(tp1_diamond[0], tp1_diamond[1], 'o', color='orange', markersize=8)

            # 标注过辊A切点的x坐标
            ax.text(tp1_roller[0]+1, tp1_roller[1]+2,
                   f'x={tp1_roller[0]:.2f}', fontsize=9, color='orange', fontweight='bold')

            # 如果有第二条有效切线，也绘制出来
            if cross['tangent2'] is not None:
                tp2_roller, tp2_diamond = cross['tangent2']
                ax.plot([tp2_roller[0], tp2_diamond[0]],
                       [tp2_roller[1], tp2_diamond[1]],
                       'purple', linewidth=2, alpha=0.6, linestyle='--',
                       label=f'交叉公切线2 (x={tp2_roller[0]:.2f})')
                ax.plot(tp2_roller[0], tp2_roller[1], 'o', color='purple', markersize=6)
                ax.plot(tp2_diamond[0], tp2_diamond[1], 'o', color='purple', markersize=6)
        
        ax.set_xlim(-40, 40)
        ax.set_ylim(-20, 90)
        ax.legend()
        
        plt.tight_layout()
        plt.show()
        
        # 打印详细信息
        print(f"\n=== 角度 {angle_deg}° 切线分析 ===")
        print(f"接触圆: {labels[result['circle_index']]}")
        print(f"圆心位置: ({result['center'][0]:.2f}, {result['center'][1]:.2f})")
        print(f"圆半径: {result['radius']}mm")
        print(f"到过辊距离: {result['distance']:.2f}mm")
        
        if result['external']:
            print(f"外公切线长度: {result['external']['length']:.2f}mm")
        if result['cross']:
            print(f"交叉公切线长度: {result['cross']['length']:.2f}mm")


def test_comprehensive_analysis():
    """测试综合切线分析"""
    
    analyzer = ComprehensiveTangentAnalyzer()
    
    # 测试几个关键角度
    test_angles = [0, 30, 60, 90, 120, 150]
    
    for angle in test_angles:
        print(f"\n{'='*50}")
        print(f"分析角度: {angle}°")
        print(f"{'='*50}")
        analyzer.visualize_tangents_at_angle(angle)


if __name__ == "__main__":
    print("综合切线分析 - 过辊与内切圆的动态切线计算")
    test_comprehensive_analysis()
    print("\n分析完成！")
