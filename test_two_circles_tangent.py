#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两圆外切线计算
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


def calculate_external_tangent_between_circles(center1, radius1, center2, radius2):
    """计算两个圆之间的外切线"""
    # 计算圆心距离
    d = np.linalg.norm(center2 - center1)
    
    # 检查是否可以有外切线
    if d <= abs(radius1 - radius2):
        return None  # 一个圆在另一个圆内部
    
    if d < radius1 + radius2:
        return None  # 两圆相交，无外切线
    
    # 计算外切线
    dx = center2[0] - center1[0]
    dy = center2[1] - center1[1]
    
    # 计算外切线的角度
    # 对于外切线，切线与连心线的夹角满足：sin(α) = |r1 - r2| / d
    sin_alpha = abs(radius1 - radius2) / d
    alpha = np.arcsin(sin_alpha)
    
    # 连心线的角度
    base_angle = np.arctan2(dy, dx)
    
    # 两条外切线的方向角
    if radius1 >= radius2:
        angle1 = base_angle + alpha
        angle2 = base_angle - alpha
    else:
        angle1 = base_angle + np.pi - alpha
        angle2 = base_angle + np.pi + alpha
    
    # 计算切点
    # 第一条切线
    normal1 = np.array([-np.sin(angle1), np.cos(angle1)])
    tp1_circle1 = center1 + radius1 * normal1
    tp1_circle2 = center2 + radius2 * normal1
    
    # 第二条切线
    normal2 = np.array([-np.sin(angle2), np.cos(angle2)])
    tp2_circle1 = center1 + radius1 * normal2
    tp2_circle2 = center2 + radius2 * normal2
    
    # 计算切线长度
    tangent_length = np.linalg.norm(tp1_circle2 - tp1_circle1)
    
    return {
        'tangent1': (tp1_circle1, tp1_circle2),
        'tangent2': (tp2_circle1, tp2_circle2),
        'length': tangent_length
    }


def test_tangent_calculation():
    """测试切线计算"""
    
    # 测试用例：过辊A和一个内切圆
    roller_center = np.array([0.5, 80.0])
    roller_radius = 2.0
    
    # 一个内切圆（锐角）
    diamond_center = np.array([-27.85, 0.0])
    diamond_radius = 0.8
    
    # 计算外切线
    result = calculate_external_tangent_between_circles(
        roller_center, roller_radius, diamond_center, diamond_radius)
    
    if result is None:
        print("无法计算外切线")
        return
    
    # 可视化
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    
    # 绘制过辊
    roller_circle = Circle(roller_center, roller_radius, 
                          fill=False, edgecolor='blue', linewidth=2, label='过辊A')
    ax.add_patch(roller_circle)
    ax.plot(roller_center[0], roller_center[1], 'bo', markersize=8)
    ax.text(roller_center[0]+2, roller_center[1]+2, 'A', fontsize=12, fontweight='bold')
    
    # 绘制内切圆
    diamond_circle = Circle(diamond_center, diamond_radius, 
                           fill=False, edgecolor='red', linewidth=2, label='内切圆')
    ax.add_patch(diamond_circle)
    ax.plot(diamond_center[0], diamond_center[1], 'ro', markersize=8)
    ax.text(diamond_center[0]+2, diamond_center[1]+2, '内切圆', fontsize=12, fontweight='bold')
    
    # 绘制外切线
    tp1_roller, tp1_diamond = result['tangent1']
    tp2_roller, tp2_diamond = result['tangent2']
    
    # 第一条外切线
    ax.plot([tp1_roller[0], tp1_diamond[0]], [tp1_roller[1], tp1_diamond[1]], 
           'g-', linewidth=3, alpha=0.8, label=f'外切线1 (L={result["length"]:.2f}mm)')
    ax.plot(tp1_roller[0], tp1_roller[1], 'go', markersize=6)
    ax.plot(tp1_diamond[0], tp1_diamond[1], 'go', markersize=6)
    
    # 第二条外切线
    ax.plot([tp2_roller[0], tp2_diamond[0]], [tp2_roller[1], tp2_diamond[1]], 
           'orange', linewidth=3, alpha=0.8, label=f'外切线2 (L={result["length"]:.2f}mm)')
    ax.plot(tp2_roller[0], tp2_roller[1], 'o', color='orange', markersize=6)
    ax.plot(tp2_diamond[0], tp2_diamond[1], 'o', color='orange', markersize=6)
    
    # 绘制连心线
    ax.plot([roller_center[0], diamond_center[0]], [roller_center[1], diamond_center[1]], 
           'k--', alpha=0.5, label='连心线')
    
    ax.set_xlim(-35, 10)
    ax.set_ylim(-10, 90)
    ax.legend()
    ax.set_title('两圆外切线计算测试', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.show()
    
    # 打印计算结果
    print(f"过辊中心: {roller_center}")
    print(f"过辊半径: {roller_radius}mm")
    print(f"内切圆中心: {diamond_center}")
    print(f"内切圆半径: {diamond_radius}mm")
    print(f"圆心距离: {np.linalg.norm(diamond_center - roller_center):.3f}mm")
    print(f"外切线长度: {result['length']:.3f}mm")
    print(f"切线1 - 过辊切点: {tp1_roller}")
    print(f"切线1 - 内切圆切点: {tp1_diamond}")
    print(f"切线2 - 过辊切点: {tp2_roller}")
    print(f"切线2 - 内切圆切点: {tp2_diamond}")


def test_multiple_circles():
    """测试多个内切圆的情况"""
    
    # 过辊参数
    roller_center = np.array([0.5, 80.0])
    roller_radius = 2.0
    
    # 内切圆参数（从之前计算得出）
    circle_centers = np.array([
        [-27.85, 0.00],    # V1 锐角圆心
        [-17.69, 8.00],    # V2 钝角圆心
        [17.69, 8.00],     # V3 钝角圆心
        [27.85, 0.00],     # V4 锐角圆心
        [17.69, -8.00],    # V5 钝角圆心
        [-17.69, -8.00],   # V6 钝角圆心
    ])
    
    radii = [0.8, 12.0, 12.0, 0.8, 12.0, 12.0]
    colors = ['red', 'green', 'green', 'red', 'green', 'green']
    labels = ['V1(锐角)', 'V2(钝角)', 'V3(钝角)', 'V4(锐角)', 'V5(钝角)', 'V6(钝角)']
    
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    
    # 绘制过辊
    roller_circle = Circle(roller_center, roller_radius, 
                          fill=False, edgecolor='blue', linewidth=3, label='过辊A')
    ax.add_patch(roller_circle)
    ax.plot(roller_center[0], roller_center[1], 'bo', markersize=10)
    ax.text(roller_center[0]+2, roller_center[1]+2, 'A', fontsize=14, fontweight='bold')
    
    # 计算并绘制每个内切圆的外切线
    for i, (center, radius, color, label) in enumerate(zip(circle_centers, radii, colors, labels)):
        # 绘制内切圆
        circle = Circle(center, radius, fill=False, edgecolor=color, linewidth=2, alpha=0.7)
        ax.add_patch(circle)
        ax.plot(center[0], center[1], 'o', color=color, markersize=6)
        ax.text(center[0]+1, center[1]+1, label, fontsize=10, color=color, fontweight='bold')
        
        # 计算外切线
        result = calculate_external_tangent_between_circles(
            roller_center, roller_radius, center, radius)
        
        if result:
            tp1_roller, tp1_diamond = result['tangent1']
            tp2_roller, tp2_diamond = result['tangent2']
            
            # 选择下方的切线（Y坐标较小的）
            if tp1_roller[1] < tp2_roller[1]:
                selected_roller = tp1_roller
                selected_diamond = tp1_diamond
            else:
                selected_roller = tp2_roller
                selected_diamond = tp2_diamond
            
            # 绘制选中的切线
            ax.plot([selected_roller[0], selected_diamond[0]], 
                   [selected_roller[1], selected_diamond[1]], 
                   color=color, linewidth=2, alpha=0.8)
            
            # 标注切线长度
            mid_x = (selected_roller[0] + selected_diamond[0]) / 2
            mid_y = (selected_roller[1] + selected_diamond[1]) / 2
            ax.text(mid_x, mid_y, f'{result["length"]:.1f}', 
                   fontsize=9, color=color, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    ax.set_xlim(-40, 40)
    ax.set_ylim(-20, 90)
    ax.legend()
    ax.set_title('过辊A与所有内切圆的外切线', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    print("测试两圆外切线计算...")
    
    # 单个圆的测试
    print("\n=== 单个内切圆测试 ===")
    test_tangent_calculation()
    
    # 多个圆的测试
    print("\n=== 多个内切圆测试 ===")
    test_multiple_circles()
    
    print("\n测试完成！")
