#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态切线长度分析 - 考虑内切圆的过辊切点计算
分析不同角度下过辊到内切圆的切线长度变化
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
import matplotlib.animation as animation

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class DynamicTangentAnalyzer:
    def __init__(self):
        """初始化动态切线分析器"""
        
        # 过辊参数
        self.roller_A = np.array([0.5, 80.0])  # 过辊A位置
        self.roller_radius = 2.0  # 过辊半径
        
        # 原始菱形顶点
        self.original_vertices = np.array([
            [-30, 0],   # V1 (左锐角)
            [-20, -4],  # V2 (左下钝角)
            [20, -4],   # V3 (右下钝角)
            [30, 0],    # V4 (右锐角)
            [20, 4],    # V5 (右上钝角)
            [-20, 4],   # V6 (左上钝角)
        ])
        
        # 内切圆参数
        self.sharp_radius = 0.8   # 锐角内切圆半径
        self.blunt_radius = 12.0  # 钝角内切圆半径
        
        # 内切圆圆心（从之前计算得出）
        self.circle_centers = np.array([
            [-27.85, 0.00],    # V1 锐角圆心
            [-17.69, 8.00],    # V2 钝角圆心
            [17.69, 8.00],     # V3 钝角圆心
            [27.85, 0.00],     # V4 锐角圆心
            [17.69, -8.00],    # V5 钝角圆心
            [-17.69, -8.00],   # V6 钝角圆心
        ])
        
        # 旋转中心
        self.rotation_center = np.array([2.0, 0.0])  # X偏移2.0mm
        
    def rotate_point(self, point, angle_deg, center):
        """绕指定中心旋转点"""
        angle_rad = np.deg2rad(angle_deg)
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
        
        # 平移到原点
        translated = point - center
        
        # 旋转
        rotated = np.array([
            translated[0] * cos_a - translated[1] * sin_a,
            translated[0] * sin_a + translated[1] * cos_a
        ])
        
        # 平移回去
        return rotated + center
    
    def calculate_tangent_to_circle(self, external_point, circle_center, circle_radius):
        """计算从外部点到圆的切线"""
        # 计算距离
        d = np.linalg.norm(external_point - circle_center)
        
        if d <= circle_radius:
            return None, None, None  # 点在圆内，无切线
        
        # 计算切线长度
        tangent_length = np.sqrt(d**2 - circle_radius**2)
        
        # 计算切点角度
        alpha = np.arcsin(circle_radius / d)
        
        # 计算从圆心到外部点的角度
        dx = external_point[0] - circle_center[0]
        dy = external_point[1] - circle_center[1]
        base_angle = np.arctan2(dy, dx)
        
        # 两个切点的角度
        angle1 = base_angle + alpha
        angle2 = base_angle - alpha
        
        # 计算切点坐标
        tangent_point1 = circle_center + circle_radius * np.array([np.cos(angle1), np.sin(angle1)])
        tangent_point2 = circle_center + circle_radius * np.array([np.cos(angle2), np.sin(angle2)])
        
        return tangent_point1, tangent_point2, tangent_length
    
    def find_closest_circle_and_tangent(self, angle_deg):
        """找到最接近的内切圆和对应的切线"""
        # 旋转所有圆心
        rotated_centers = []
        for center in self.circle_centers:
            rotated_center = self.rotate_point(center, angle_deg, self.rotation_center)
            rotated_centers.append(rotated_center)
        
        # 计算到每个圆的切线
        tangent_results = []
        for i, center in enumerate(rotated_centers):
            radius = self.sharp_radius if i in [0, 3] else self.blunt_radius
            
            # 计算切线
            tp1, tp2, length = self.calculate_tangent_to_circle(self.roller_A, center, radius)
            
            if tp1 is not None:
                # 选择合适的切点（通常选择X坐标较大的，即更靠左的切点）
                if tp1[0] > tp2[0]:
                    selected_tangent_point = tp1
                else:
                    selected_tangent_point = tp2
                
                tangent_results.append({
                    'circle_index': i,
                    'center': center,
                    'radius': radius,
                    'tangent_point': selected_tangent_point,
                    'tangent_length': length,
                    'distance_to_roller': np.linalg.norm(center - self.roller_A)
                })
        
        if not tangent_results:
            return None
        
        # 选择距离过辊最近的圆（或其他选择策略）
        closest_result = min(tangent_results, key=lambda x: x['distance_to_roller'])
        
        return closest_result
    
    def analyze_tangent_variation(self, angle_range=(0, 360), angle_step=1):
        """分析切线长度随角度的变化"""
        angles = np.arange(angle_range[0], angle_range[1] + angle_step, angle_step)
        
        results = {
            'angles': [],
            'tangent_lengths': [],
            'circle_indices': [],
            'tangent_points': [],
            'circle_centers': []
        }
        
        for angle in angles:
            result = self.find_closest_circle_and_tangent(angle)
            
            if result:
                results['angles'].append(angle)
                results['tangent_lengths'].append(result['tangent_length'])
                results['circle_indices'].append(result['circle_index'])
                results['tangent_points'].append(result['tangent_point'])
                results['circle_centers'].append(result['center'])
        
        return results
    
    def visualize_dynamic_tangent(self, angles_to_show=[0, 30, 60, 90, 120, 150]):
        """可视化不同角度下的动态切线"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for idx, angle in enumerate(angles_to_show):
            if idx >= len(axes):
                break
                
            ax = axes[idx]
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_title(f'角度: {angle}°', fontsize=12, fontweight='bold')
            
            # 绘制过辊
            roller_circle = Circle(self.roller_A, self.roller_radius, 
                                 fill=False, edgecolor='blue', linewidth=2)
            ax.add_patch(roller_circle)
            ax.plot(self.roller_A[0], self.roller_A[1], 'bo', markersize=8, label='过辊A')
            
            # 旋转并绘制菱形轮廓
            rotated_vertices = []
            for vertex in self.original_vertices:
                rotated_vertex = self.rotate_point(vertex, angle, self.rotation_center)
                rotated_vertices.append(rotated_vertex)
            rotated_vertices = np.array(rotated_vertices)
            
            diamond_poly = Polygon(rotated_vertices, fill=False, edgecolor='gray', 
                                 linewidth=1, linestyle='--', alpha=0.5)
            ax.add_patch(diamond_poly)
            
            # 绘制内切圆
            colors = ['red', 'green', 'green', 'red', 'green', 'green']
            for i, center in enumerate(self.circle_centers):
                rotated_center = self.rotate_point(center, angle, self.rotation_center)
                radius = self.sharp_radius if i in [0, 3] else self.blunt_radius
                color = colors[i]
                
                circle = Circle(rotated_center, radius, fill=False, 
                              edgecolor=color, linewidth=1, alpha=0.7)
                ax.add_patch(circle)
                ax.plot(rotated_center[0], rotated_center[1], 'o', 
                       color=color, markersize=4)
            
            # 计算并绘制当前角度的切线
            result = self.find_closest_circle_and_tangent(angle)
            if result:
                # 绘制切线
                ax.plot([self.roller_A[0], result['tangent_point'][0]], 
                       [self.roller_A[1], result['tangent_point'][1]], 
                       'r-', linewidth=3, alpha=0.8, label='切线')
                
                # 标注切点
                ax.plot(result['tangent_point'][0], result['tangent_point'][1], 
                       'ro', markersize=6)
                
                # 标注切线长度
                mid_x = (self.roller_A[0] + result['tangent_point'][0]) / 2
                mid_y = (self.roller_A[1] + result['tangent_point'][1]) / 2
                ax.text(mid_x, mid_y + 2, f'L={result["tangent_length"]:.2f}mm', 
                       fontsize=10, fontweight='bold', 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.8))
                
                # 高亮当前接触的圆
                highlight_circle = Circle(result['center'], result['radius'], 
                                        fill=True, facecolor='red', alpha=0.3)
                ax.add_patch(highlight_circle)
            
            ax.set_xlim(-40, 40)
            ax.set_ylim(-20, 90)
            ax.legend()
        
        plt.tight_layout()
        plt.suptitle('动态切线长度分析 - 不同角度下的过辊切线变化', 
                    fontsize=16, fontweight='bold', y=0.98)
        plt.show()
    
    def plot_tangent_length_curve(self):
        """绘制切线长度随角度变化的曲线"""
        # 分析完整的角度范围
        results = self.analyze_tangent_variation(angle_range=(0, 360), angle_step=0.5)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
        
        # 上图：切线长度变化曲线
        ax1.plot(results['angles'], results['tangent_lengths'], 'b-', linewidth=2)
        ax1.set_xlabel('旋转角度 (度)')
        ax1.set_ylabel('切线长度 (mm)')
        ax1.set_title('切线长度随旋转角度的变化', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # 标注最大值和最小值
        max_idx = np.argmax(results['tangent_lengths'])
        min_idx = np.argmin(results['tangent_lengths'])
        
        ax1.plot(results['angles'][max_idx], results['tangent_lengths'][max_idx], 
                'ro', markersize=8)
        ax1.text(results['angles'][max_idx], results['tangent_lengths'][max_idx] + 1, 
                f'最大值: {results["tangent_lengths"][max_idx]:.2f}mm\n角度: {results["angles"][max_idx]}°', 
                ha='center', fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='red', alpha=0.3))
        
        ax1.plot(results['angles'][min_idx], results['tangent_lengths'][min_idx], 
                'go', markersize=8)
        ax1.text(results['angles'][min_idx], results['tangent_lengths'][min_idx] - 3, 
                f'最小值: {results["tangent_lengths"][min_idx]:.2f}mm\n角度: {results["angles"][min_idx]}°', 
                ha='center', fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='green', alpha=0.3))
        
        # 下图：接触的圆索引变化
        ax2.plot(results['angles'], results['circle_indices'], 'r-', linewidth=2, marker='o', markersize=3)
        ax2.set_xlabel('旋转角度 (度)')
        ax2.set_ylabel('接触圆索引')
        ax2.set_title('接触圆切换情况', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        ax2.set_yticks(range(6))
        ax2.set_yticklabels(['V1(锐角)', 'V2(钝角)', 'V3(钝角)', 'V4(锐角)', 'V5(钝角)', 'V6(钝角)'])
        
        plt.tight_layout()
        plt.show()
        
        # 打印统计信息
        print(f"\n切线长度变化分析:")
        print(f"最大切线长度: {np.max(results['tangent_lengths']):.3f}mm (角度: {results['angles'][max_idx]}°)")
        print(f"最小切线长度: {np.min(results['tangent_lengths']):.3f}mm (角度: {results['angles'][min_idx]}°)")
        print(f"平均切线长度: {np.mean(results['tangent_lengths']):.3f}mm")
        print(f"切线长度变化范围: {np.max(results['tangent_lengths']) - np.min(results['tangent_lengths']):.3f}mm")


if __name__ == "__main__":
    # 创建动态切线分析器
    analyzer = DynamicTangentAnalyzer()
    
    # 可视化不同角度下的切线
    print("生成动态切线可视化...")
    analyzer.visualize_dynamic_tangent()
    
    # 绘制切线长度变化曲线
    print("分析切线长度变化...")
    analyzer.plot_tangent_length_curve()
    
    print("动态切线分析完成！")
